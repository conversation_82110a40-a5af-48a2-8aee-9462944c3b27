# <img src="https://img.alicdn.com/tfs/TB1JpgJRFXXXXc7XpXXXXXXXXXX-800-800.png" width="60"/> @ali/rxpi-newcomer-zone

![@ali/rxpi-newcomer-zone@ALINPM](http://web.npm.alibaba-inc.com/badge/v/@ali/rxpi-newcomer-zone.svg?style=flat-square) ![@ali/rxpi-newcomer-zone@ALINPM](http://web.npm.alibaba-inc.com/badge/d/@ali/rxpi-newcomer-zone.svg?style=flat-square)

> 新人专区

- 👤 桑陆 <<EMAIL>>

## Support

<img alt="browser" src="https://gw.alicdn.com/tfs/TB1uYFobGSs3KVjSZPiXXcsiVXa-200-200.svg" width="25px" height="25px" /> <img alt="weex" src="https://gw.alicdn.com/tfs/TB1jM0ebMaH3KVjSZFjXXcFWpXa-200-200.svg" width="25px" height="25px" /> <img alt="miniApp" src="https://gw.alicdn.com/tfs/TB1bBpmbRCw3KVjSZFuXXcAOpXa-200-200.svg" width="25px" height="25px" />

## Install

```
tnpm i @ali/rxpi-newcomer-zone --save
```

## Usage

```js
import { createElement } from 'rax';
import NewcomerZone from '@ali/rxpi-newcomer-zone';

export default function App() {
  return (
    <NewcomerZone prop={true} />
  );
}
```

## Props

| Prop | Type | Required | Default | Description | Support |
|:---|:---|:---|:---|:---|:---|:---|:---|
| **`prop`** | `string/number/bool...` | `Y/N` | `true/undefined` | This is a prop | <img alt="browser" src="https://gw.alicdn.com/tfs/TB1uYFobGSs3KVjSZPiXXcsiVXa-200-200.svg" width="25px" height="25px" /> <img alt="weex" src="https://gw.alicdn.com/tfs/TB1jM0ebMaH3KVjSZFjXXcFWpXa-200-200.svg" width="25px" height="25px" /> <img alt="miniApp" src="https://gw.alicdn.com/tfs/TB1bBpmbRCw3KVjSZFuXXcAOpXa-200-200.svg" width="25px" height="25px" /> |

## API

| Name | Arguments | Return | Description |
|:---|:---|:---|:---|:---|
| **`doSomething`** | `(x: number, y: bool)` | `undefined` | do something |

## Dev

* `tnpm i` 安装依赖 `node modules`
* `clam newbranch` 开新分支
* `clam push` 提交代码
* `clam dev` 启动本地服务，自动打开默认浏览器开发调试
* `clam dev:miniapp` 调试小程序
* `clam build` 执行构建
* `clam prepub` 执行构建并推送远程 `daily/x.y.z` 分支;
* `clam publish` 执行构建推送远程 `daily/x.y.z` 分支并推送 `publish/x.y.z` tag，同时发布到 tnpm `@ali` 私有域下

## Demo

- <https://edith.wapa.taobao.com/proxy/basement/rxpi/newcomer-zone/demo/index.html>

![](http://gcodex.alicdn.com/qrcode.do?biz_code=common&redirect=0&size=150&content=https%3A%2F%2Fedith.wapa.taobao.com%2Fproxy%2Fbasement%2Frxpi%2Fnewcomer-zone%2Fdemo%2Findex.html)

## ChangeLog

见 [CHANGELOG.md](http://gitlab.alibaba-inc.com/rxpi/newcomer-zone/blob/master/CHANGELOG.md)。
