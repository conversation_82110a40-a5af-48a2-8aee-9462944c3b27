import { createElement, useMemo } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import IconFontMiniapp from '@ali/rxpi-icon-font-miniapp';
import CouponItem from './components/coupon-item/index';
import ItemModule from './components/item/index';
import './index.less';

export default function NewPeople(props) {
  const { 
    backgroundImg, 
    rightTopBtnText, 
    rightTopBtnUrl, 
    coupons = [], 
    itemList = [], 
    couponApplyUrl,
    isShow
  } = props.data;

  const couponList = useMemo(() => {
    return itemList.length ? coupons.slice(0, 2) : coupons.slice(0, 4);
  }, [JSON.stringify(coupons), itemList.length]);

  const couponSize = useMemo(() => {
    const couponLength = couponList.length;
    const itemLength = itemList.length;
    if (itemLength < 1 && couponLength <= 2) {
      return 'large';
    }
    if (itemLength < 1 && couponLength === 3) {
      return 'middle';
    }
    return 'small';
  }, [itemList.length, couponList.length]);

  return (
    <>
      <View 
        className="newcomer-zone" 
        x-if={isShow} 
        style={{ backgroundImage: `url(${backgroundImg})` }}
      >
        {/* 右上角文案 */}
        <TrackerLink
          className="newcomer-zone__right-btn"
          data-spm={props.spmc}
          data-spm-click={`gostr=/tbtrip;locaid=newPeople_topRightBtn`}
          data-need-exp
          href={rightTopBtnUrl}
        >
          <Text className="newcomer-zone__right-btn-text">{rightTopBtnText}</Text>
          <IconFontMiniapp
            codePoint={'&#xe602;'}
            style={{
              fontSize: '12rpx',
              padding: '0 6rpx'
            }}
          />
        </TrackerLink>

        {/* 券和商品卡 */}
        <View className="newcomer-zone__content">
          <View className="newcomer-zone__coupons" x-if={couponList && couponList.length}>
            <CouponItem
              x-for={(item, index) in couponList}
              key={index}
              item={item}
              index={index}
              couponSize={couponSize}
              spmc={props.spmc}
            />
          </View>
          <ItemModule x-if={itemList.length} itemList={itemList} spmc={props.spmc} />
        </View>
      </View>
    </>
  );
}
