import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import IconFontMiniapp from '@ali/rxpi-icon-font-miniapp';
import CouponModule from './components/coupon/index';
import ItemModule from './components/item/index';
import './index.less';

export default function NewPeople(props) {
  const { 
    backgroundImg, 
    rightTopBtnText, 
    rightTopBtnUrl, 
    coupons = [], 
    itemList = [], 
    couponApplyUrl,
    isShow
  } = props.data;

  return (
    <>
      <View 
        className="newcomer-zone-new-user-wrap" 
        x-if={isShow} 
        style={{ backgroundImage: `url(${backgroundImg})` }}
      >
        <TrackerLink
          className="top-right-btn"
          data-spm={props.spmc}
          data-spm-click={`gostr=/tbtrip;locaid=newPeople_topRightBtn`}
          data-need-exp
          href={rightTopBtnUrl}
        >
          <Text className="top-right-btn-text">{rightTopBtnText}</Text>
          <IconFontMiniapp
            codePoint={'&#xe602;'}
            style={{
              fontSize: '12rpx',
              padding: '0 6rpx'
            }}
          />
        </TrackerLink>
        <View className="new-user-coupon-content">
          <CouponModule 
            couponList={itemList.length ? coupons.slice(0, 2) : coupons.slice(0, 4)} 
            itemLength={itemList.length} 
            spmc={props.spmc}
            couponApplyUrl={couponApplyUrl}
          />
          <ItemModule itemList={itemList} spmc={props.spmc} />
        </View>
      </View>
    </>
  );
}
