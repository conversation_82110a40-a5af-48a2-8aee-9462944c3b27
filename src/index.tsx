import { createElement, useEffect, useMemo, useState } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import { useEmitter } from '@ali/rxpi-emitter';
import IconFontMiniapp from '@ali/rxpi-icon-font-miniapp';
import CouponItem from './components/coupon-item/index';
import ItemModule from './components/item/index';
import CountDown from './components/count-down';
import './index.less';

/**
 * 根据行业tab对券进行分类
 * @param coupons 所有配置的券
 * @param curBiz 当前锚定的tab
 * @returns 
 */
function getSortedCoupons(coupons, curBiz: string) {
  return coupons.reduce((prev, coupon) => {
    const { biz } = coupon;
    // 平台券
    if (biz === 'platform') {
      prev.commonCoupons = [...(prev.commonCoupons || []), coupon];
    } else if (biz === curBiz) {
      prev.bizCoupons = [...(prev.bizCoupons || []), coupon];
    } else {
      prev.otherCoupons = [...(prev.otherCoupons || []), coupon];
    }
    return prev;
  }, {});
}

export default function NewcomerZone(props) {
  const { 
    backgroundImg, 
    rightTopBtnText, 
    rightTopBtnUrl, 
    coupons = [], 
    itemList = [],
    isShow,
    countDownEndTime,
  } = props.data;
  const [biz, setBiz] = useState(props.initCurrentTab || '');
  const emitter = useEmitter();

  /** 最终的优惠券列表 */
  const couponList = useMemo(() => {
    const targetLength = itemList.length ? 2 : 4;
    const sliceIndex = targetLength - 1;
    const { commonCoupons = [], bizCoupons = [], otherCoupons = [] } = getSortedCoupons(coupons, biz);
    // 展示n-1张行业券，留一个坑给平台券和行业券，行业券优先
    return [...bizCoupons.slice(0, sliceIndex), ...commonCoupons, ...otherCoupons].slice(0, targetLength);
  }, [JSON.stringify(coupons), itemList.length, biz]);

  /** 券尺寸 */
  const couponSize = useMemo(() => {
    const couponLength = couponList.length;
    // 一排四
    if (couponLength > 3 || itemList.length) {
      return 'small';
    }
    // 一排三
    if (couponLength === 3) {
      return 'middle';
    }
    // 一排二
    return 'large';
  }, [couponList.length, itemList.length]);

  const onTabChange = ({ tabName }) => {
    setBiz(tabName);
  };

  useEffect(() => {
    emitter.on('onCurrTabChange|page', onTabChange);

    return () => {
      emitter.off('onCurrTabChange|page', onTabChange);
    };
  }, []);

  return (
    <>
      <View 
        className="newcomer-zone" 
        x-if={isShow} 
        style={{ backgroundImage: `url(${backgroundImg})` }}
      >
        {/* 倒计时 */}
        <CountDown endTime={countDownEndTime} />

        {/* 右上角文案 */}
        <TrackerLink
          className="newcomer-zone__right-btn"
          data-spm={props.spmc}
          data-spm-click={`gostr=/tbtrip;locaid=newPeople_topRightBtn`}
          data-need-exp
          href={rightTopBtnUrl}
        >
          <Text className="newcomer-zone__right-btn-text">{rightTopBtnText}</Text>
          <IconFontMiniapp
            codePoint={'&#xe602;'}
            style={{
              fontSize: '12rpx',
              color: '#ffffff',
              padding: '0 6rpx'
            }}
          />
        </TrackerLink>

        {/* 券和商品卡 */}
        <View className="newcomer-zone__content">
          <View className="newcomer-zone__coupons" x-if={couponList && couponList.length}>
            <CouponItem
              // @ts-ignore
              x-for={(item, index) in couponList}
              // @ts-ignore
              key={index}
              // @ts-ignore
              item={item}
              // @ts-ignore
              index={index}
              couponSize={couponSize}
              spmc={props.spmc}
            />
          </View>
          <ItemModule x-if={itemList.length} itemList={itemList} spmc={props.spmc} />
        </View>
      </View>
    </>
  );
}
