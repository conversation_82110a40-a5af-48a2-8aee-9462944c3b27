import User from '@ali/rxpi-user';

App({
  onLaunch(options) {
    this.User = User;
    this.resetWxLoginSDK(options && options.scene, true);
  },
  resetWxLoginSDK() {
    // 初始化登录
    User.resetWxLoginSDK({
      // appId, // 小程序appId
      appName: 'trip', // 会员服务端分配
      appEntrance: 'weixin', // 会员服务端分配
      gateway: `https://passport.feizhu.com`, // 服务端提供
      pagePath: '/pages/login/webview',
      needPassWebViewCookie: false,
    });
    this.login = User.getWxLoginSDK();
    // 静默登录
    User.trySlientLogin(() => {
      this.loginInited = true;
    });
  },
}); // eslint-disable-line
