/** @jsx createElement */
import { createElement, useEffect } from 'rax';
import { isMiniApp } from 'universal-env';
import RecyclerView from 'rax-recyclerview';
import Text from 'rax-text';
import Image from 'rax-image';
import View from 'rax-view';

import NewcomerZone from '../../index';
import './index.css';

function App() {
  const source = { uri: 'https://gw.alicdn.com/tfs/TB1JpgJRFXXXXc7XpXXXXXXXXXX-800-800.png' };

  useEffect(function () {
    if (isMiniApp) {
      my.setNavigationBar({
        title: 'NewcomerZone - Demo',
      });
    }
  }, []);

  return (
    <RecyclerView className="container" showsHorizontalScrollIndicator={false} showsVerticalScrollIndicator={false}>
      <RecyclerView.Cell>
        <View className="component-title">
          <Image className="logo" source={source} resizeMode="contain" />
          <Text>NewcomerZone</Text>
        </View>
      </RecyclerView.Cell>

      <RecyclerView.Cell>
        <View className="demo-section">
          <Text className="category">基本使用</Text>
          <NewcomerZone />
        </View>
      </RecyclerView.Cell>
    </RecyclerView>
  );
}

export default App;
