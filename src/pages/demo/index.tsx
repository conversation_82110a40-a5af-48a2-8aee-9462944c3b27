/** @jsx createElement */
import { createElement, useEffect, useState } from 'rax';
import { isMiniApp } from 'universal-env';
import View from 'rax-view';

import NewcomerZone from '../../index';
import { MOCK_DATA } from './mock';
import { BizType } from '../../types';
import './index.css';

function App() {
  const [newcomerData, setNewcomerData] = useState(null);

  const fetchData = () => {
    if (isMiniApp) {
      setNewcomerData(MOCK_DATA);
      return;
    }
    setNewcomerData(MOCK_DATA);
  };

  useEffect(function () {
    fetchData();
  }, []);

  return (
    <View className="container">
      <NewcomerZone x-if={newcomerData} bizType={BizType.HOTEL} data={newcomerData} />
    </View>
  );
}

export default App;
