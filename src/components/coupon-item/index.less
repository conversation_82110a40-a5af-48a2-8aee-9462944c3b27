.newcomer-zone__coupon {
  position: relative;
  height: 140rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  
  &--large {
    width: 327rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i1/O1CN01bGPxUf24ETf6JAl4L_!!6000000007359-2-tps-327-140.png');
  }
  
  &--middle {
    width: 214rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01CoQcRJ1YwNvW9KFIH_!!6000000003123-2-tps-214-140.png');
  }
  
  &--small {
    width: 158rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01aN8ru41TcAMdgqW6V_!!6000000002402-2-tps-158-140.png');
  }
  
  &-tag {
    position: absolute;
    right: 4rpx;
    top: -15rpx;
    font-size: 20rpx;
    font-weight: bold;
    color: white;
    line-height: 28rpx;
    padding: 0 6rpx;
    border: 1px solid white;
    border-radius: 12rpx 12rpx 12rpx 0;
    background: #6666ff;
  }
  
  &-top {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    color: #ff5533;
    margin-top: 2rpx;
    height: 50rpx;
  }
  
  &-prefix {
    font-size: 24rpx;
    font-weight: bold;
    line-height: 24rpx;
  }
  
  &-price {
    font-size: 36rpx;
    font-weight: bold;
    line-height: 50rpx;
  }
  
  &-suffix {
    font-size: 24rpx;
    font-weight: bold;
    line-height: 24rpx;
  }
  
  &-title {
    width: 100%;
    color: #642c24;
    font-size: 22rpx;
    height: 31rpx;
    line-height: 30rpx;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 6rpx;
  }
  
  &-btn {
    height: 36rpx;
    background: white;
    border-radius: 18rpx;
    margin-top: 10rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0f131a;
    font-size: 20rpx;
    font-weight: bold;
    padding: 0 18rpx;
  }
}
