import { createElement, useState } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import { isWeChatMiniProgram } from '@ali/rxpi-env';
import Hubble from '@ali/rxpi-hubble';
import { showToast } from '@ali/rxpi-toast-api';
import { getData } from '@ali/rxpi-utils';
import { ICoupon } from '../../types';
import './index.less';

interface IProps {
  item: ICoupon;
  couponSize: string;
  spmc: string;
  index: number;
  extraArgs?: Record<string, string | number>;
}

export default function CouponItem(props: IProps) {
  const { item, index,  couponSize, spmc, extraArgs = {} } = props;
  const [needApply, setNeedApply] = useState(Number(item.status) === 0);

  // 领券
  const onApplyCoupon = () => {
    if (!needApply) return;
    const { code, rightGroupCode, safeCode: asac, prodChannelCode } = item;
    const api = isWeChatMiniProgram ? 'mtop.fliggy.tripbp.award.apply4wx' : 'mtop.fliggy.tripbp.award.apply';
    Hubble.request({
      api,
      v: '1.0',
      needLogin: true,
      secType: 2,
      ext_headers: { asac },
      data: {
        appName: 'newcomer-zone',
        asac,
        activityId: code,
        failedRollBack: true,
        prodChannelCode,
        awardRightGroupId: rightGroupCode,
        extInfo: JSON.stringify({
          //b 发奖 j 抽奖 h 兑奖
          sendWayExtType: 'a',
        }),
      }
    }).then((res: any) => {
      const actionResult = getData(res, 'data.actionResult');
      if (!actionResult) {
        return Promise.reject({
          api: getData(res, 'api'),
          code: getData(res, 'data.failCode'),
          message: getData(res, 'data.failMsg'),
        });
      }
      showToast('领取成功');
      setNeedApply(false);
      return res; // 显式返回值
    }).catch((e: any) => {
      const errMsg = e && e.api ? `${e.api}: ${e.code},${e.message}` : e;
      console.error(`newcomerZone ${errMsg}`);
      showToast('领取失败');
    })
  };

  return (
    <TrackerLink
      key={index}
      index={index}
      data-need-exp
      data-spm={spmc}
      data-biz-args={extraArgs}
      data-spm-click={`gostr=/tbtrip;locaid=newPeople_coupon_${needApply ? 'apply' : 'use'}_${index}`}
      href={needApply ? '' : item.couponUseUrl}
      style={{ marginLeft: index === 0 ? '' : '12rpx' }}
      x-class={['newcomer-zone__coupon', `newcomer-zone__coupon--${couponSize}`]}
      onClick={onApplyCoupon}
    >
      <Text x-if={item.cornerMark} className="newcomer-zone__coupon-tag">{item.cornerMark}</Text>

      <View className="newcomer-zone__coupon-top">
        <Text className="newcomer-zone__coupon-prefix">¥</Text>
        <Text className="newcomer-zone__coupon-price">{item.faceValue || ''}</Text>
      </View>

      <Text className="newcomer-zone__coupon-title">{(item.couponName || '').slice(0, 5)}</Text>

      <Text className="newcomer-zone__coupon-btn">{needApply ? '领取' : '去使用'}</Text>
    </TrackerLink>
  );
}
