import { createElement, useState } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import { isWeChatMiniProgram, isWeb } from '@ali/rxpi-env';
import Hubble from '@ali/rxpi-hubble';
import { showToast } from '@ali/rxpi-toast-api';
import './index.less';

interface IProps {
  item: {
    cornerMark: string;
    couponName: string;
    faceValue: string;
    status: string;
    couponUseUrl: string;
    code: string;
    rightGroupCode: string;
    asac: string;
  };
  couponSize: string;
  spmc: string;
  index: number;
}

/** 获取领券参数 */
function getParamList(subActIds, prodChannelCode) {
  const extra = { prodChannelCode };
  if (isWeChatMiniProgram && !isWeb) {
    Object.assign(extra, { extInfo: { wechatSendCouponType: 'native' }});
  }
  return JSON.stringify(subActIds.split(',').map(activityId => ({ activityId, ...extra })));
}

export default function CouponItem(props: IProps) {
  const { item, index,  couponSize, spmc } = props;
  const [applied, setApplied] = useState(item.status === '0');

  // 领券
  const onApplyCoupon = () => {
    const { code, rightGroupCode, asac } = item;
    const api = isWeChatMiniProgram ? 'mtop.fliggy.tripbp.wx.award.batchApply' : 'mtop.fliggy.tripbp.award.batchApply';
    Hubble.request({
      api,
      v: '1.0',
      needLogin: true,
      secType: 2,
      ext_headers: { asac },
      data: {
        appName: 'newcomer-zone',
        asac,
        paramList: getParamList(code, rightGroupCode),
      }
    }).then((res) => {
      showToast('领取成功');
      setApplied(true);
    }).catch(e => {
      showToast(e.message || e.errMsg || '领取失败');
    })
  };

  return (
    <TrackerLink
      key={index}
      index={index}
      data-need-exp
      data-spm={spmc}
      data-spm-click={`gostr=/tbtrip;locaid=newPeople_coupon_${applied ? 'apply' : 'use'}_${index}`}
      href={applied ? '' : item.couponUseUrl}
      style={{ marginLeft: index === 0 ? '' : '12rpx' }}
      x-class={['newcomer-zone__coupon', `newcomer-zone__coupon--${couponSize}`]}
      onClick={onApplyCoupon}
    >
      <Text x-if={item.cornerMark} className="newcomer-zone__coupon-tag">{item.cornerMark}</Text>

      <View className="newcomer-zone__coupon-top">
        <Text className="newcomer-zone__coupon-prefix">¥</Text>
        <Text className="newcomer-zone__coupon-price">{item.faceValue || ''}</Text>
      </View>

      <Text className="newcomer-zone__coupon-title">{(item.couponName || '').slice(0, 5)}</Text>

      <Text className="newcomer-zone__coupon-btn">{applied ? '领取' : '去使用'}</Text>
    </TrackerLink>
  );
}
