import { createElement, useState } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import './index.less';

interface IProps {
  spmc: string;
  itemList: Array<{
    jumpUrl: string;
    picUrl: string;
    title: string;
    subTitle: string;
    buttonText: string;
  }>;
}

export default function ItemModule(props: IProps) {
  const { itemList = [], spmc } = props || {};

  const [activeIndex, setActiveIndex] = useState(0);

  return (
    <View className="newcomer-zone-item-module-content" x-if={itemList.length}>
      <swiper
        className="item-module-swiper"
        autoplay={true}
        loop={true}
        interval={3000}
        bindchange={(e) => {
          setActiveIndex(e.detail.current);
        }}
      >
        <swiper-item x-for={(item, index) in itemList} key={index}>
          <TrackerLink
            className="item-module-box"
            data-spm={spmc}
            data-spm-click={`gostr=/tbtrip;locaid=newPeople_item_${index}`}
            data-need-exp
            href={item.jumpUrl}
            style={{
              background: `url(${item.picUrl}) no-repeat`,
              backgroundPosition: 'center',
              backgroundSize: 'cover',
            }}
          >
            <Text className="item-module-box-title">{item.title || ''}</Text>
            <Text className="item-module-box-subtitle">{item.subTitle || ''}</Text>
            <Text className="item-module-box-btn">{item.buttonText || ''}</Text>
          </TrackerLink>
        </swiper-item>
      </swiper>

      <View className="item-module-swiper-pagination">
        {itemList.map((_, index) => (
          <View
            key={index}
            className={`item-module-swiper-bullet ${index === activeIndex ? 'item-module-swiper-bullet-active' : ''}`}
          />
        ))}
      </View>
    </View>
  );
}
