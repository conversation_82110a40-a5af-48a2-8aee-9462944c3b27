# CountDown 倒计时组件

一个功能完整的倒计时组件，支持大于1天时展示纯文案，小于1天时展示秒级倒计时。

## 功能特性

- ✅ 大于1天时展示纯文案（如"还有3天"）
- ✅ 小于1天时展示秒级倒计时（时:分:秒格式）
- ✅ 自动刷新，精确到秒
- ✅ 倒计时结束回调

## 基本用法

```tsx
import CountDown from '@/components/count-down';

// 基本使用
<CountDown 
  endTime="2024-12-31 23:59:59"
  onEnd={() => console.log('倒计时结束')}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| endTime | 结束时间 | `number \| string \| Date` | - |
| onEnd | 倒计时结束回调 | `() => void` | - |


## 注意事项

1. `endTime` 支持多种格式：时间戳、日期字符串、Date 对象
2. 组件会自动处理时区问题
3. 倒计时结束后会显示"活动已结束"
4. 组件会在卸载时自动清理定时器，避免内存泄漏
