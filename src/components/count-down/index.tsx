import { createElement, useMemo } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import IconFontMiniapp from '@ali/rxpi-icon-font-miniapp';
import CouponItem from './components/coupon-item/index';
import ItemModule from './components/item/index';
import './index.less';

export default function CountDown(props) {
  return (
    <View className="">

    </View>
  )
}