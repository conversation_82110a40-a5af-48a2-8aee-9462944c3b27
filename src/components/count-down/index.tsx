import { createElement, useState, useEffect, useRef } from 'rax';
import View from 'rax-view';
import './index.less';

interface CountDownProps {
  endTime: number | string | Date; // 结束时间
  onEnd?: () => void; // 倒计时结束回调
}

function addZero(number) {
  if (number > 9) {
    return number;
  }
  return `0${number}`;
}

const ONE_DAY = 1000 * 60 * 60 * 24;

export default function CountDown(props: CountDownProps) {
  const { endTime, onEnd } = props;
  const [text, setText] = useState('');
  const timerRef = useRef(null);

  // 计算剩余时间
  const calculateTime = () => {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const duration = end - now;

    // 倒计时结束
    if (duration <= 0) {
      setText('00:00:00');
      onEnd && onEnd();
      return false;
    }

    // 大于1天，展示纯文案
    if (duration > ONE_DAY) {
      setText(`${Math.floor(duration / ONE_DAY)}天`);
      return false;
    }

    const hours = Math.floor((duration % ONE_DAY) / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((duration % (1000 * 60)) / 1000);

    setText(`${addZero(hours)}:${addZero(minutes)}:${addZero(seconds)}`);
    return true;
  };

  // 处理时间
  const handleTime = () => {
    const needUpdate = calculateTime();
    if (needUpdate) {
      runCountDown();
    }
  };

  // 运行倒计时
  const runCountDown = () => {
    timerRef.current = setTimeout(() => {
      clearTimeout(timerRef.current);
      handleTime();
    }, 1000);
  }

  useEffect(() => {
    // 设置定时器
    handleTime();
    // 清理定时器
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [endTime]);

  return (
    <View className="newcomer-zone__countdown">
      {text}后失效
    </View>
  );
}