import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import './index.less';

export default function CouponModule(props) {
  const { couponList, itemLength, spmc, couponApplyUrl } = props;
  return (
    <View className="newcomer-zone__coupons" x-if={couponList.length}>
      <TrackerLink
        x-for={(item, index) in couponList}
        key={index}
        index={index}
        data-need-exp
        data-spm={spmc}
        data-spm-click={`gostr=/tbtrip;locaid=newPeople_coupon_${item.status === '0' ? 'apply' : 'use'}_${index}`}
        href={item.status === '0' ? couponApplyUrl : item.couponUseUrl}
        style={{ marginLeft: index === 0 ? '' : '12rpx' }}
        x-class={{
          'newcomer-zone__coupon': true,
          'newcomer-zone__coupon--large': itemLength < 1 && couponList.length <= 2,
          'newcomer-zone__coupon--middle': itemLength < 1 && couponList.length === 3,
          'newcomer-zone__coupon--small': itemLength > 0 || couponList.length > 3,
        }}
      >
        <Text x-if={item.cornerMark} className="newcomer-zone__coupon-tag">{item.cornerMark}</Text>

        <View className="newcomer-zone__coupon-top">
          <Text className="newcomer-zone__coupon-prefix">¥</Text>
          {/* <Text x-if={item.couponType === 1} className="coupon-item-prefix">¥</Text> */}
          <Text className="newcomer-zone__coupon-price">{item.faceValue || ''}</Text>
          {/* <Text x-if={item.couponType === 2} className="coupon-item-suffix">折</Text> */}
        </View>

        <Text className="newcomer-zone__coupon-title">{(item.couponName || '').slice(0, 5)}</Text>

        <Text className="newcomer-zone__coupon-btn">{item.status === '0' ? '领取' : '去使用'}</Text>
      </TrackerLink>
    </View>
  );
}
