import { createElement, useMemo, Fragment, useRef, useState } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import Image from 'rax-image';
import Picture from '@ali/rxpi-picture';
import TrackerLink from '@ali/rxpi-tracker-link';
import './index.less';

export default function CouponModule(props) {
  const { couponList, itemLength, spmc, couponApplyUrl } = props;
  return (
    <View className="wechat-crowd-coupon-module-content" x-if={couponList.length}>
      <TrackerLink
        x-for={(item, index) in couponList}
        key={index}
        index={index}
        data-need-exp
        data-spm={spmc}
        data-spm-click={`gostr=/tbtrip;locaid=newPeople_coupon_${item.status === '0' ? 'apply' : 'use'}_${index}`}
        href={item.status === '0' ? couponApplyUrl : item.couponUseUrl}
        style={{ marginLeft: index === 0 ? '' : '12rpx' }}
        x-class={{
          'wechat-crowd-coupon-item': true,
          'wechat-crowd-coupon-item-large': itemLength < 1 && couponList.length <= 2,
          'wechat-crowd-coupon-item-middle': itemLength < 1 && couponList.length === 3,
          'wechat-crowd-coupon-item-small-1': itemLength > 0 || couponList.length > 3,
        }}
      >
        <Text x-if={item.cornerMark} className="wechat-crowd-coupon-item-tag">{item.cornerMark}</Text>

        <View className="wechat-crowd-coupon-item-top">
          <Text className="wechat-crowd-coupon-item-prefix">¥</Text>
          {/* <Text x-if={item.couponType === 1} className="coupon-item-prefix">¥</Text> */}
          <Text className="wechat-crowd-coupon-item-price">{item.faceValue || ''}</Text>
          {/* <Text x-if={item.couponType === 2} className="coupon-item-suffix">折</Text> */}
        </View>

        <Text className="wechat-crowd-coupon-item-title">{(item.couponName || '').slice(0, 5)}</Text>

        <Text className="wechat-crowd-coupon-item-btn">{item.status === '0' ? '领取' : '去使用'}</Text>
      </TrackerLink>
    </View>
  );
}
