import { createElement, useState } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TrackerLink from '@ali/rxpi-tracker-link';
import { IItem } from '../../types';
import './index.less';

interface IProps {
  spmc: string;
  itemList: IItem[];
  extraArgs?: Record<string, string | number>;
}

export default function ItemModule(props: IProps) {
  const { itemList = [], spmc } = props || {};
  const [activeIndex, setActiveIndex] = useState(0);
  
  const onSwiperChange = (e) => {
    setActiveIndex(e.detail.current);
  };

  return (
    <View className="newcomer-zone__item" x-if={itemList.length}>
      <swiper
        className="newcomer-zone__swiper"
        autoplay={true}
        loop={true}
        circular={true}
        interval={3000}
        onChange={onSwiperChange}
      >
        {/* @ts-ignore */}
        <swiper-item x-for={(item, index) in itemList} key={index}>
          <TrackerLink
            className="newcomer-zone__swiper-item"
            data-spm={spmc}
            // @ts-ignore
            data-spm-click={`gostr=/tbtrip;locaid=newPeople_item_${index}`}
            data-need-exp
            // @ts-ignore
            href={item.jumpUrl}
            // @ts-ignore
            style={{ backgroundImage: `url(${item.picUrl})` }}
          >
            {/* @ts-ignore */}
            <Text className="newcomer-zone__swiper-title">{item.title || ''}</Text>
            {/* @ts-ignore */}
            <Text className="newcomer-zone__swiper-subtitle">{item.subTitle || ''}</Text>
            {/* @ts-ignore */}
            <Text className="newcomer-zone__swiper-btn">{item.buttonText || ''}</Text>
          </TrackerLink>
        </swiper-item>
      </swiper>

      <View className="newcomer-zone__swiper-pagination">
        {itemList.map((_, index) => (
          <View
            key={index}
            className={`newcomer-zone__swiper-bullet ${index === activeIndex ? 'newcomer-zone__swiper-bullet--active' : ''}`}
          />
        ))}
      </View>
    </View>
  );
}
