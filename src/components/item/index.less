.newcomer-zone__item {
  position: relative;
  width: 327rpx;
  height: 140rpx;
}

.newcomer-zone__swiper {
  position: relative;
  width: 327rpx;
  height: 140rpx;
  
  &-pagination {
    position: absolute;
    bottom: 12rpx;
    left: 18rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    z-index: 10;
  }
  
  &-bullet {
    width: 12px;
    height: 3px;
    border-radius: 2rpx 0 2rpx 0;
    background: rgba(255, 255, 255, 0.8);
    margin-right: 3px;

    &--active {
      background: #ffe033;
    }
  }
  
  &-item {
    position: relative;
    width: 327rpx;
    height: 140rpx;
    border-radius: 12rpx;
    overflow: hidden;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
  }
  
  &-title {
    width: 190rpx;
    font-size: 26rpx;
    font-weight: bold;
    color: white;
    line-height: 36rpx;
    margin: 18rpx 0 0 18rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  &-subtitle {
    width: 180rpx;
    font-size: 22rpx;
    color: rgba(255, 255, 255, 0.8);
    line-height: 30rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 18rpx;
  }
  
  &-btn {
    position: absolute;
    bottom: 6rpx;
    right: 18rpx;
    max-width: 156rpx;
    height: 36rpx;
    font-size: 20rpx;
    font-weight: bold;
    color: #0f131a;
    background: white;
    border-radius: 18rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 18rpx;
    overflow: hidden;
    white-space: nowrap;
  }
}
