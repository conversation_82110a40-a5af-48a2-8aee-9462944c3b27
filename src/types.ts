export enum BizType {
  HOTEL = 'hotel',
  FLIGHT = 'flight',
  TRAIN_BUS = 'trainBus',
  RENT_CAR = 'rentCar',
  TICKET = 'ticket',
  TRAVEL = 'travel',
}

export interface ICoupon {
  biz: string;
  code: string;
  cornerMark: string;
  couponName: string;
  faceValue: string;
  status: string;
  prodChannelCode: string;
  couponUseUrl: string;
  rightGroupCode: string;
  safeCode: string;
}

export interface IItem {
  jumpUrl: string;
  picUrl: string;
  title: string;
  subTitle: string;
  buttonText: string;
}

export interface IProps {
  bizType?: BizType,
  spmc?: string;
  data?: {
    backgroundImg: string;
    rightTopBtnText: string;
    rightTopBtnUrl: string;
    coupons: ICoupon[];
    itemList?: IItem[];
    isShow: boolean;
    expireTime: string;
  };
  extraArgs?: Record<string, string | number>;
}