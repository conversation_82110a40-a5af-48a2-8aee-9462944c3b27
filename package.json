{"name": "@ali/rxpi-newcomer-zone", "widgetName": "newcomer-zone", "version": "1.0.0", "description": "新人专区", "main": "lib/index.js", "module": "es/index.js", "files": ["es", "lib", "src"], "universal": true, "miniprogram": ".", "miniappConfig": {"main": "lib/miniapp/index", "main:wechat": "lib/miniapp-wechat/index"}, "clam2Version": "1.9.53", "lib": "rax", "type": "weex", "rxpiType": "component", "group": "rxpi", "repository": {"type": "git", "url": "**************************:rxpi/newcomer-zone.git"}, "keywords": ["newcomer-zone"], "author": {"name": "桑陆", "email": "<EMAIL>"}, "license": "BSD", "devDependencies": {"@types/ali-app": "^1.0.1", "@types/rax": "^1.0.0", "typescript": "^4.9.5", "babel-eslint": "^8.2.5", "@ali/eslint-config-fliggy": "^1.1.3", "eslint": "^6.8.0", "eslint-plugin-babel": "^5.1.0", "eslint-plugin-jsx-plus": "^0.1.0", "eslint-plugin-react": "^7.10.0", "husky": "^8.0.3", "lint-staged": "^13.1.2", "prettier": "^2.8.4", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^3.4.1", "rax-recyclerview": "^1.0.0", "driver-universal": "^3.0.0", "jsx2mp-runtime": "^0.4.0"}, "dependencies": {"@ali/rxpi-env": "^2.4.11", "@ali/rxpi-hubble": "^1.9.1", "@ali/rxpi-icon-font-miniapp": "^1.0.7", "@ali/rxpi-picture": "^4.2.1", "@ali/rxpi-toast-api": "^1.1.1", "@ali/rxpi-tracker-link": "^2.11.39", "babel-runtime-jsx-plus": "^0.1.4", "rax": "^1.0.0", "rax-image": "^2.0.0", "rax-text": "^2.0.0", "rax-view": "^2.0.0", "universal-env": "^3.0.0"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "lint-staged": {"*.{js,jsx,ts,tsx,md,html,css,less,scss}": "prettier --write"}, "scripts": {"check-types": "tsc --project tsconfig.check.json", "prepare": "if [ -d .git ]; then husky install; else echo '当前目录不是一个 Git 仓库，跳过 husky install'; fi"}, "clam": {"inlineStyle": false}, "toolkit": "@ali/clam-toolkit-rxpi", "defPublishType": "pegasus", "commandType": "clam"}